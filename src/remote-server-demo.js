const WebSocket = require('ws');
const crypto = require('crypto');
const WebSocketProtoHelper = require('./ws-proto-example');
const {eventCallback} = require("./demo_common");
const express = require('express');
const path = require('path');

class RemoteWebSocketServer {
    constructor(port = 8085, httpPort = 3000) {
        this.port = port;
        this.httpPort = httpPort;
        this.wss = null;
        this.httpServer = null;
        this.clients = new Map(); // clientId -> {ws, info}
        this.activeConnections = new Map(); // connectionId -> {clientId, liveUrl, status, timestamp}
        this.isRunning = false;

        // 初始化protobuf辅助类
        this.protoHelper = new WebSocketProtoHelper();

        // 初始化Express应用
        this.app = express();
        this.setupHttpServer();
    }

    // 设置HTTP服务器
    setupHttpServer() {
        // 静态文件服务
        this.app.use(express.static(path.join(__dirname, '../public')));

        // API路由
        this.app.get('/api/clients', (req, res) => {
            const clients = this.getClientsInfo();
            res.json(clients);
        });

        this.app.get('/api/connections', (req, res) => {
            const connections = this.getActiveConnections();
            res.json(connections);
        });

        this.app.post('/api/connect', express.json(), (req, res) => {
            const { clientId, liveUrl } = req.body;
            if (!clientId || !liveUrl) {
                return res.status(400).json({ error: 'Missing clientId or liveUrl' });
            }
            const requestId = this.requestLiveRoom(clientId, liveUrl);
            res.json({ success: true, requestId });
        });

        this.app.post('/api/disconnect', express.json(), (req, res) => {
            const { clientId, connectionId } = req.body;
            if (!clientId) {
                return res.status(400).json({ error: 'Missing clientId' });
            }
            const requestId = this.requestDisconnectRoom(clientId, connectionId);
            res.json({ success: true, requestId });
        });

        this.app.post('/api/status', express.json(), (req, res) => {
            const { clientId } = req.body;
            if (!clientId) {
                return res.status(400).json({ error: 'Missing clientId' });
            }
            const requestId = this.requestStatus(clientId);
            res.json({ success: true, requestId });
        });

        this.app.post('/api/broadcast', express.json(), (req, res) => {
            const { message } = req.body;
            if (!message) {
                return res.status(400).json({ error: 'Missing message' });
            }
            const broadcastMessage = this.protoHelper.createServerBroadcastMessage(message);
            const buffer = this.protoHelper.serializeServerMessage(broadcastMessage);
            this.broadcast(buffer);
            res.json({ success: true });
        });

        this.app.post('/api/show-window', express.json(), (req, res) => {
            const { clientId, connectionId, show } = req.body;
            if (!clientId || !connectionId) {
                return res.status(400).json({ error: 'Missing clientId or connectionId' });
            }
            const requestId = this.requestShowWindow(clientId, connectionId, show !== false);
            res.json({ success: true, requestId });
        });
    }

// connect f2c16defd371865c https://www.tiktok.com/@speakerlul4/live
// disconnect f2c16defd371865c https://www.tiktok.com/@popmart.usshop/live
    start() {
        if (this.isRunning) {
            console.log('Remote WebSocket server is already running');
            return;
        }

        // 启动HTTP服务器
        this.httpServer = this.app.listen(this.httpPort, () => {
            console.log(`HTTP server started on port ${this.httpPort}`);
            console.log(`Frontend available at: http://localhost:${this.httpPort}`);
        });

        this.wss = new WebSocket.Server({ port: this.port });
        this.isRunning = true;

        this.wss.on('connection', (ws, req) => {
            const clientId = crypto.randomBytes(8).toString('hex');
            console.log(`New client connected: ${clientId} from ${req.socket.remoteAddress}`);
            
            this.clients.set(clientId, {
                ws: ws,
                info: {
                    id: clientId,
                    connectedAt: new Date().getTime(),
                    context: null,
                    totalConnections: 0
                }
            });

            // 发送欢迎消息
            this.sendWelcomeMessage(ws, clientId);

            ws.on('message', (message) => {
                try {
                    // 使用protobuf解析消息
                    this.handleProtobufClientMessage(clientId, message);
                } catch (error) {
                    console.error('Error parsing client message:', error);
                    this.sendErrorMessage(ws, 'Invalid message format');
                }
            });

            ws.on('close', () => {
                console.log(`Client disconnected: ${clientId}`);
                this.clients.delete(clientId);
            });

            ws.on('error', (error) => {
                console.error(`WebSocket error for client ${clientId}:`, error);
                this.clients.delete(clientId);
            });

            // TODO: remove test,连接发起监听
            this.requestLiveRoom(clientId, "https://www.tiktok.com/@jacquie11111/live");
        });

        this.wss.on('error', (error) => {
            console.error('WebSocket Server error:', error);
        });

        console.log(`Remote WebSocket server started on port ${this.port}`);
        console.log('Ready to accept LiveWebSocketBridge clients...');
    }

    stop() {
        if (!this.isRunning) {
            console.log('Remote WebSocket server is not running');
            return;
        }

        // 关闭所有客户端连接
        this.clients.forEach(({ws}, clientId) => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
        this.clients.clear();

        // 关闭服务器
        if (this.wss) {
            this.wss.close(() => {
                console.log('Remote WebSocket server stopped');
            });
        }

        if (this.httpServer) {
            this.httpServer.close(() => {
                console.log('HTTP server stopped');
            });
        }

        this.isRunning = false;
    }

    // 发送欢迎消息
    sendWelcomeMessage(ws, clientId) {
        const message = this.protoHelper.createWelcomeMessage(
            'Connected to Remote LiveApi WebSocket Server', clientId
        );
        const buffer = this.protoHelper.serializeServerMessage(message);
        ws.send(buffer);
    }

    // 发送错误消息
    sendErrorMessage(ws, errorMessage) {
        const message = this.protoHelper.createServerErrorMessage(errorMessage);
        const buffer = this.protoHelper.serializeServerMessage(message);
        ws.send(buffer);
    }

    // 处理protobuf客户端消息
    handleProtobufClientMessage(clientId, data) {
        try {
            const message = this.protoHelper.deserializeClientMessage(data);
            const messageType = this.protoHelper.getMessageType(message);
            console.log(`Received protobuf from client ${clientId}:`, messageType);

            // 直接处理protobuf消息，不转换为JSON
            this.handleProtobufMessage(clientId, message, messageType);
        } catch (error) {
            console.error('Error handling protobuf client message:', error);
        }
    }

    // 直接处理protobuf消息
    handleProtobufMessage(clientId, message, messageType) {
        switch (messageType) {
            case 'client_info':
                this.handleClientInfo(clientId, {
                    context: message.clientInfo.context,
                    timestamp: message.clientInfo.timestamp
                });
                break;

            case 'live_events':
                this.handleLiveEvents(clientId, {
                    connectionId: message.liveEvents.connectionId,
                    liveUrl: message.liveEvents.liveUrl,
                    data: JSON.parse(message.liveEvents.data),
                    timestamp: message.liveEvents.timestamp,
                    source: message.liveEvents.source,
                    context: message.liveEvents.context
                });
                break;

            case 'heartbeat':
                this.handleHeartbeat(clientId, {
                    context: message.heartbeat.context,
                    totalConnections: message.heartbeat.totalConnections,
                    timestamp: message.heartbeat.timestamp
                });
                break;

            case 'error':
                this.handleError(clientId, {
                    connectionId: message.error.connectionId,
                    liveUrl: message.error.liveUrl,
                    error: message.error.error,
                    timestamp: message.error.timestamp,
                    context: message.error.context
                });
                break;

            case 'custom':
                this.handleCustom(clientId, {
                    data: JSON.parse(message.custom.data),
                    timestamp: message.custom.timestamp,
                    source: message.custom.source,
                    context: message.custom.context
                });
                break;

            case 'live_room_response':
                this.handleLiveRoomResponse(clientId, {
                    requestId: message.liveRoomResponse.requestId,
                    success: message.liveRoomResponse.success,
                    connection_id: message.liveRoomResponse.connectionId,
                    live_url: message.liveRoomResponse.liveUrl,
                    error: message.liveRoomResponse.error
                });
                break;

            case 'disconnect_room_response':
                this.handleDisconnectRoomResponse(clientId, {
                    requestId: message.disconnectRoomResponse.requestId,
                    success: message.disconnectRoomResponse.success,
                    connection_id: message.disconnectRoomResponse.connectionId,
                    error: message.disconnectRoomResponse.error
                });
                break;

            case 'status_response':
                this.handleStatusResponse(clientId, {
                    requestId: message.statusResponse.requestId,
                    status: message.statusResponse.status
                });
                break;

            case 'show_window_response':
                this.handleShowWindowResponse(clientId, {
                    requestId: message.showWindowResponse.requestId,
                    success: message.showWindowResponse.success,
                    connection_id: message.showWindowResponse.connectionId,
                    error: message.showWindowResponse.error,
                    show: message.showWindowResponse.show
                });
                break;

            default:
                console.log(`Unknown protobuf message type: ${messageType}`);
                break;
        }
    }

    // 处理客户端信息
    handleClientInfo(clientId, data) {
        const client = this.clients.get(clientId);
        if (!client) return;

        client.info.context = data.context;
        console.log(`Client ${clientId} context: ${data.context}`);
    }

    // 处理直播事件
    handleLiveEvents(clientId, data) {
        const client = this.clients.get(clientId);
        if (!client) return;

        try {
            eventCallback(data.data);

            data.data.events_data.forEach(event => {
                switch (event.msg_type) {
                    case 5: {// 直播消息
                        const _control = event.control_msg;
                        switch (_control.action) {
                            case 0: {
                                console.log(_control.room_name + ' 正在直播 ' + JSON.stringify(_control));
                                break;
                            }
                            case 1: {
                                console.log('直播结束 ', data.connectionId);
                                this.requestDisconnectRoom(clientId, data.connectionId);
                                break;
                            }
                            case 100: {
                                console.log('url错误 ' + _control.live_url + ',' + _control.action_msg);
                                break;
                            }
                            case 101: {
                                console.log('其他错误 ' + _control.live_url + ',' + _control.action_msg);
                                break;
                            }
                        }
                        break;
                    }
                }
            });
        } catch (e) {
            console.error('Error processing live events:', e);
        }
    }

    // 处理心跳消息
    handleHeartbeat(clientId, data) {
        const client = this.clients.get(clientId);
        if (!client) return;

        client.info.totalConnections = data.totalConnections;
        console.log(`Heartbeat from ${clientId}: ${data.totalConnections} connections`);
    }

    // 处理错误消息
    handleError(clientId, data) {
        console.error(`Error from ${clientId}:`, data.error);
    }

    // 处理自定义消息
    handleCustom(clientId, data) {
        console.log(`Custom message from ${clientId}:`, data.data);
    }

    // 处理状态响应
    handleStatusResponse(clientId, statusInfo) {
        if (statusInfo && statusInfo.connections) {
            statusInfo.connections.forEach(conn => {
                this.activeConnections.set(conn.id, {
                    clientId: clientId,
                    liveUrl: conn.liveUrl,
                    status: conn.status,
                    windowVisible: conn.windowVisible,
                    timestamp: Date.now()
                });
            });
            console.log(`Updated ${statusInfo.connections.length} connections for client ${clientId}`);
        }
    }

    // 处理直播间连接响应
    handleLiveRoomResponse(clientId, data) {
        console.log(`Live room response from ${clientId}:`, data);
        if (data.success && data.connection_id) {
            this.activeConnections.set(data.connection_id, {
                clientId: clientId,
                liveUrl: data.live_url,
                status: 'active',
                windowVisible: true,
                timestamp: Date.now()
            });
            console.log(`Live room connected: ${data.connection_id} -> ${data.live_url}`);
        } else if (!data.success) {
            console.log(`Live room connection failed: ${data.error || 'Unknown error'}`);
        }
    }

    // 处理断开连接响应
    handleDisconnectRoomResponse(clientId, data) {
        console.log(`Disconnect room response from ${clientId}:`, data);
        if (data.success && data.connection_id) {
            this.activeConnections.delete(data.connection_id);
            console.log(`Live room disconnected: ${data.connection_id}`);
        }
    }

    // 处理显示/隐藏窗口响应
    handleShowWindowResponse(clientId, data) {
        console.log(`Show window response from ${clientId}:`, data);
        if (data.success) {
            console.log(`Window ${data.show ? 'shown' : 'hidden'} for connection: ${data.connection_id}`);
            // 更新连接状态中的窗口可见性
            const connection = this.activeConnections.get(data.connection_id);
            if (connection) {
                connection.windowVisible = data.show;
                connection.timestamp = Date.now();
            }
        } else {
            console.log(`Failed to ${data.show ? 'show' : 'hide'} window: ${data.error || 'Unknown error'}`);
        }
    }

    // 处理客户端消息（已弃用，现在使用protobuf）
    handleClientMessage(clientId, data) {
        // 这个方法已经被protobuf处理替代
        // 保留用于向后兼容，但建议使用handleProtobufMessage
        console.log(`Legacy JSON message from ${clientId}:`, data.type);
        console.log('Warning: JSON messages are deprecated, please use protobuf');

        // 只处理一些基本的消息类型
        switch (data.type) {
            case 'log':
                console.log(`Log from ${clientId}: ${data.message}`);
                break;
            case 'client_shutdown':
                console.log(`Client ${clientId} is shutting down`);
                break;
            default:
                console.log(`Unhandled legacy message type: ${data.type}`);
        }
    }

    // 发送消息给特定客户端
    sendToClient(clientId, data) {
        const client = this.clients.get(clientId);
        if (!client || client.ws.readyState !== WebSocket.OPEN) {
            console.warn(`Cannot send message to client ${clientId}: not connected`);
            return;
        }

        try {
            // data应该已经是序列化的buffer
            client.ws.send(data);
        } catch (error) {
            console.error(`Error sending message to client ${clientId}:`, error);
            this.clients.delete(clientId);
        }
    }

    // 广播消息给所有客户端
    broadcast(data) {
        let sentCount = 0;
        this.clients.forEach(({ws}, clientId) => {
            if (ws.readyState === WebSocket.OPEN) {
                try {
                    // data应该已经是序列化的buffer
                    ws.send(data);
                    sentCount++;
                } catch (error) {
                    console.error(`Error broadcasting to client ${clientId}:`, error);
                    this.clients.delete(clientId);
                }
            } else {
                this.clients.delete(clientId);
            }
        });
        console.log(`Broadcasted message to ${sentCount} clients`);
    }

    // 请求客户端连接到直播间
    requestLiveRoom(clientId, liveUrl, options = {}) {
        const requestId = crypto.randomBytes(8).toString('hex');

        const message = this.protoHelper.createLiveRoomRequestMessage(
            liveUrl, requestId, JSON.stringify(options)
        );
        const buffer = this.protoHelper.serializeServerMessage(message);
        this.sendToClient(clientId, buffer);

        console.log(`Requested client ${clientId} to connect to live room: ${liveUrl}`);
        return requestId;
    }

    // 请求客户端断开直播间连接
    requestDisconnectRoom(clientId, connectionId = null) {
        const requestId = crypto.randomBytes(8).toString('hex');

        const message = this.protoHelper.createDisconnectRoomRequestMessage(
            connectionId, requestId
        );
        const buffer = this.protoHelper.serializeServerMessage(message);
        this.sendToClient(clientId, buffer);

        console.log(`Requested client ${clientId} to disconnect from live room: ${connectionId || 'all'}`);
        return requestId;
    }

    // 请求客户端状态
    requestStatus(clientId) {
        const requestId = crypto.randomBytes(8).toString('hex');

        const message = this.protoHelper.createGetStatusRequestMessage(requestId);
        const buffer = this.protoHelper.serializeServerMessage(message);
        this.sendToClient(clientId, buffer);

        console.log(`Requested status from client ${clientId}`);
        return requestId;
    }

    // 请求显示/隐藏窗口
    requestShowWindow(clientId, connectionId, show = true) {
        const requestId = crypto.randomBytes(8).toString('hex');

        const message = this.protoHelper.createShowWindowRequestMessage(
            connectionId, requestId, show
        );
        const buffer = this.protoHelper.serializeServerMessage(message);
        this.sendToClient(clientId, buffer);

        console.log(`Requested client ${clientId} to ${show ? 'show' : 'hide'} window for connection: ${connectionId}`);
        return requestId;
    }

    // 获取所有客户端信息
    getClientsInfo() {
        const clients = [];
        this.clients.forEach(({info}) => {
            clients.push(info);
        });
        return clients;
    }

    // 获取活跃连接信息
    getActiveConnections() {
        const connections = [];
        this.activeConnections.forEach((connection, connectionId) => {
            connections.push({
                connectionId: connectionId,
                ...connection
            });
        });
        return connections;
    }
}

// 如果直接运行此文件，则启动服务器
if (require.main === module) {
    console.log('🚀 启动远程WebSocket服务器...');
    console.log('');

    const server = new RemoteWebSocketServer(8087, 3001);
    server.start();

    // 添加交互式命令
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    console.log('');
    console.log('📋 可用命令:');
    console.log('  clients - 显示所有连接的客户端');
    console.log('  connect <clientId> <liveUrl> - 请求客户端连接到直播间');
    console.log('  disconnect <clientId> [connectionId] - 请求客户端断开连接');
    console.log('  status <clientId> - 请求客户端状态');
    console.log('  broadcast <message> - 广播消息给所有客户端');
    console.log('  quit - 退出服务器');
    console.log('');

    rl.on('line', (input) => {
        const [command, ...args] = input.trim().split(' ');

        switch (command.toLowerCase()) {
            case 'clients':
                const clients = server.getClientsInfo();
                console.log('📋 连接的客户端:');
                clients.forEach(client => {
                    console.log(`  - ${client.id}: ${client.context || 'unknown'} (${client.totalConnections} connections)`);
                });
                break;

            case 'connect':
                if (args.length < 2) {
                    console.log('❌ 用法: connect <clientId> <liveUrl>');
                } else {
                    const [clientId, liveUrl] = args;
                    server.requestLiveRoom(clientId, liveUrl);
                }
                break;

            case 'disconnect':
                if (args.length < 1) { // 停止监听指定直播间，或者所有直播间
                    console.log('❌ 用法: disconnect <clientId> [connectionId]');
                } else {
                    const [clientId, connectionId] = args;
                    server.requestDisconnectRoom(clientId, connectionId);
                }
                break;

            case 'status':
                if (args.length < 1) {
                    console.log('❌ 用法: status <clientId>');
                } else {
                    const [clientId] = args;
                    server.requestStatus(clientId);
                }
                break;

            case 'broadcast':
                if (args.length === 0) {
                    console.log('❌ 用法: broadcast <message>');
                } else {
                    const message = args.join(' ');
                    const broadcastMessage = server.protoHelper.createServerBroadcastMessage(message);
                    const buffer = server.protoHelper.serializeServerMessage(broadcastMessage);
                    server.broadcast(buffer);
                }
                break;

            case 'quit':
                console.log('👋 关闭服务器...');
                server.stop();
                rl.close();
                process.exit(0);
                break;

            default:
                console.log('❓ 未知命令，输入 help 查看可用命令');
        }
    });

    // 处理退出信号
    process.on('SIGINT', () => {
        console.log('\n👋 收到退出信号，关闭服务器...');
        server.stop();
        rl.close();
        process.exit(0);
    });
}

module.exports = RemoteWebSocketServer;
