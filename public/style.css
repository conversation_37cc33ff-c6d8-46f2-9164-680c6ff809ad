/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

header h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-direction: column;
}

.auto-connect-hint {
    color: #666;
    font-size: 0.8rem;
    margin-top: 5px;
}

.status-connected {
    color: #27ae60;
    font-weight: 600;
}

.status-disconnected {
    color: #e74c3c;
    font-weight: 600;
}

.status-connecting {
    color: #f39c12;
    font-weight: 600;
    animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
    from {
        opacity: 0.6;
    }
    to {
        opacity: 1;
    }
}

/* 面板样式 */
.panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.panel h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
}

.form-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 客户端列表样式 */
.clients-container {
    margin-top: 10px;
}

.clients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

#clients-count {
    color: #666;
    font-weight: 500;
}

.clients-list {
    border: 2px solid #eee;
    border-radius: 8px;
    min-height: 100px;
    padding: 15px;
}

.client-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
    border-left: 4px solid #3498db;
}

.client-item:last-child {
    margin-bottom: 0;
}

.client-id {
    font-weight: 600;
    color: #2c3e50;
}

.client-info {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

.no-clients {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
}

/* 实时数据样式 */
.data-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.live-data {
    background: #1e1e1e;
    color: #fff;
    border-radius: 8px;
    padding: 15px;
    height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 8px;
    padding: 4px 0;
}

.log-entry.system {
    color: #3498db;
}

.log-entry.client {
    color: #27ae60;
}

.log-entry.live {
    color: #f39c12;
}

.log-entry.error {
    color: #e74c3c;
}

.timestamp {
    color: #888;
    margin-right: 8px;
}

.message {
    word-wrap: break-word;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 15% auto;
    padding: 20px;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

.modal-actions {
    text-align: right;
    margin-top: 20px;
}

/* 连接历史样式 */
.history-container {
    margin-top: 10px;
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

#history-count {
    color: #666;
    font-weight: 500;
}

.connection-history {
    border: 2px solid #eee;
    border-radius: 8px;
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
    padding: 15px;
}

.history-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
    border-left: 4px solid #17a2b8;
    font-size: 0.9rem;
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-url {
    font-weight: 600;
    color: #2c3e50;
    word-break: break-all;
}

.history-info {
    color: #666;
    margin-top: 4px;
}

.no-history {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
}

/* 窗口控制样式 */
.window-control-form {
    margin-top: 10px;
}

/* 连接状态指示器 */
.connection-status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.connection-status-active {
    background-color: #27ae60;
}

.connection-status-inactive {
    background-color: #e74c3c;
}

.connection-status-unknown {
    background-color: #95a5a6;
}

/* 数据持久化指示器 */
.persistence-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1001;
}

.persistence-indicator.show {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .form-actions {
        flex-direction: column;
    }

    .clients-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .data-controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .history-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
}
